import { ImageResponse } from 'next/og'
import { NextRequest } from 'next/server'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const title = searchParams.get('title') || 'ChessTicks'
    const subtitle = searchParams.get('subtitle') || 'Professional Chess Timer'

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#121212',
            backgroundImage: 'radial-gradient(circle at 25px 25px, #333 2%, transparent 0%), radial-gradient(circle at 75px 75px, #333 2%, transparent 0%)',
            backgroundSize: '100px 100px',
            position: 'relative',
          }}
        >
          {/* Background Gradient */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(59, 130, 246, 0.15) 100%)',
            }}
          />
          
          {/* Logo/Icon */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100px',
              height: '100px',
              backgroundColor: '#22c55e',
              borderRadius: '20px',
              marginBottom: '30px',
              boxShadow: '0 15px 30px rgba(34, 197, 94, 0.4)',
            }}
          >
            <div
              style={{
                fontSize: '50px',
                color: 'white',
                fontWeight: 'bold',
              }}
            >
              ⏱️
            </div>
          </div>

          {/* Main Title */}
          <div
            style={{
              fontSize: '56px',
              fontWeight: 'bold',
              color: 'white',
              textAlign: 'center',
              marginBottom: '15px',
              maxWidth: '900px',
              lineHeight: '1.1',
            }}
          >
            {title}
          </div>

          {/* Subtitle */}
          <div
            style={{
              fontSize: '28px',
              color: '#a1a1aa',
              textAlign: 'center',
              marginBottom: '30px',
              maxWidth: '800px',
              lineHeight: '1.2',
            }}
          >
            {subtitle}
          </div>

          {/* Key Features */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '15px',
              flexWrap: 'wrap',
              maxWidth: '900px',
            }}
          >
            {['5 Timer Modes', 'Tournament Ready', 'Free & Online'].map((feature) => (
              <div
                key={feature}
                style={{
                  backgroundColor: 'rgba(34, 197, 94, 0.2)',
                  border: '2px solid rgba(34, 197, 94, 0.4)',
                  borderRadius: '10px',
                  padding: '10px 20px',
                  color: '#22c55e',
                  fontSize: '18px',
                  fontWeight: '600',
                }}
              >
                {feature}
              </div>
            ))}
          </div>

          {/* URL */}
          <div
            style={{
              position: 'absolute',
              bottom: '30px',
              right: '30px',
              fontSize: '20px',
              color: '#71717a',
              fontWeight: '500',
            }}
          >
            chessticks.vercel.app
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    )
  } catch (e: any) {
    console.log(`${e.message}`)
    return new Response(`Failed to generate the image`, {
      status: 500,
    })
  }
}
