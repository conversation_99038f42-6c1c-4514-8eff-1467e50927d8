import { ImageResponse } from 'next/og'
import { NextRequest } from 'next/server'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const title = searchParams.get('title') || 'ChessTicks - Professional Chess Timer'
    const subtitle = searchParams.get('subtitle') || 'All 5 Tournament Timer Modes | Free Chess Clock'

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#121212',
            backgroundImage: 'radial-gradient(circle at 25px 25px, #333 2%, transparent 0%), radial-gradient(circle at 75px 75px, #333 2%, transparent 0%)',
            backgroundSize: '100px 100px',
            position: 'relative',
          }}
        >
          {/* Background Pattern */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
            }}
          />
          
          {/* Logo/Icon */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '120px',
              height: '120px',
              backgroundColor: '#22c55e',
              borderRadius: '24px',
              marginBottom: '40px',
              boxShadow: '0 20px 40px rgba(34, 197, 94, 0.3)',
            }}
          >
            <div
              style={{
                fontSize: '60px',
                color: 'white',
                fontWeight: 'bold',
              }}
            >
              ⏱️
            </div>
          </div>

          {/* Main Title */}
          <div
            style={{
              fontSize: '64px',
              fontWeight: 'bold',
              color: 'white',
              textAlign: 'center',
              marginBottom: '20px',
              maxWidth: '1000px',
              lineHeight: '1.1',
            }}
          >
            {title}
          </div>

          {/* Subtitle */}
          <div
            style={{
              fontSize: '32px',
              color: '#a1a1aa',
              textAlign: 'center',
              marginBottom: '40px',
              maxWidth: '900px',
              lineHeight: '1.2',
            }}
          >
            {subtitle}
          </div>

          {/* Features */}
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: '20px',
              maxWidth: '1000px',
            }}
          >
            {['Sudden Death', 'Fischer Increment', 'Simple Delay', 'Bronstein', 'Multi-Stage'].map((feature) => (
              <div
                key={feature}
                style={{
                  backgroundColor: 'rgba(34, 197, 94, 0.2)',
                  border: '2px solid rgba(34, 197, 94, 0.3)',
                  borderRadius: '12px',
                  padding: '12px 24px',
                  color: '#22c55e',
                  fontSize: '20px',
                  fontWeight: '600',
                }}
              >
                {feature}
              </div>
            ))}
          </div>

          {/* URL */}
          <div
            style={{
              position: 'absolute',
              bottom: '40px',
              right: '40px',
              fontSize: '24px',
              color: '#71717a',
              fontWeight: '500',
            }}
          >
            chessticks.vercel.app
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    )
  } catch (e: any) {
    console.log(`${e.message}`)
    return new Response(`Failed to generate the image`, {
      status: 500,
    })
  }
}
