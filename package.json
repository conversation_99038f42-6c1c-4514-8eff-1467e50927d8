{"name": "chessticks", "version": "0.1.0", "private": true, "description": "A modern, professional chess timer application with all five major tournament timer modes", "keywords": ["chess", "timer", "tournament", "chess-clock", "react", "nextjs", "typescript"], "homepage": "https://chessticks.vercel.app", "repository": {"type": "git", "url": "https://github.com/UtkarshTheDev/ChessTicks.git"}, "bugs": {"url": "https://github.com/UtkarshTheDev/ChessTicks/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watchAll", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build", "export": "next export", "preview": "next build && next start", "postbuild": "npx next-sitemap"}, "dependencies": {"@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.6", "@types/jest": "^30.0.0", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.12.0", "lucide-react": "^0.456.0", "motion": "^11.12.0", "next": "^15.3.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sound": "^4.0.3", "zustand": "^5.0.1"}, "devDependencies": {"@types/canvas-confetti": "^1.6.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@next/bundle-analyzer": "^15.3.4", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "15.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-sitemap": "^4.2.3", "postcss": "^8", "prettier": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}