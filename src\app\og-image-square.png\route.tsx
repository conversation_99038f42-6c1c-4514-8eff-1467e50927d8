import { ImageResponse } from 'next/og'
import { NextRequest } from 'next/server'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
  try {
    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#121212',
            backgroundImage: 'radial-gradient(circle at 25px 25px, #333 2%, transparent 0%), radial-gradient(circle at 75px 75px, #333 2%, transparent 0%)',
            backgroundSize: '100px 100px',
            position: 'relative',
          }}
        >
          {/* Background Gradient */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(59, 130, 246, 0.15) 100%)',
            }}
          />
          
          {/* Logo/Icon */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '200px',
              height: '200px',
              backgroundColor: '#22c55e',
              borderRadius: '40px',
              marginBottom: '60px',
              boxShadow: '0 25px 50px rgba(34, 197, 94, 0.4)',
            }}
          >
            <div
              style={{
                fontSize: '100px',
                color: 'white',
                fontWeight: 'bold',
              }}
            >
              ⏱️
            </div>
          </div>

          {/* Main Title */}
          <div
            style={{
              fontSize: '80px',
              fontWeight: 'bold',
              color: 'white',
              textAlign: 'center',
              marginBottom: '30px',
              maxWidth: '1000px',
              lineHeight: '1.1',
            }}
          >
            ChessTicks
          </div>

          {/* Subtitle */}
          <div
            style={{
              fontSize: '40px',
              color: '#a1a1aa',
              textAlign: 'center',
              marginBottom: '60px',
              maxWidth: '900px',
              lineHeight: '1.2',
            }}
          >
            Professional Chess Timer
          </div>

          {/* Features Grid */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                display: 'flex',
                gap: '20px',
                justifyContent: 'center',
              }}
            >
              {['Sudden Death', 'Fischer'].map((feature) => (
                <div
                  key={feature}
                  style={{
                    backgroundColor: 'rgba(34, 197, 94, 0.2)',
                    border: '2px solid rgba(34, 197, 94, 0.4)',
                    borderRadius: '15px',
                    padding: '15px 30px',
                    color: '#22c55e',
                    fontSize: '24px',
                    fontWeight: '600',
                  }}
                >
                  {feature}
                </div>
              ))}
            </div>
            <div
              style={{
                display: 'flex',
                gap: '20px',
                justifyContent: 'center',
              }}
            >
              {['Delay', 'Bronstein', 'Multi-Stage'].map((feature) => (
                <div
                  key={feature}
                  style={{
                    backgroundColor: 'rgba(34, 197, 94, 0.2)',
                    border: '2px solid rgba(34, 197, 94, 0.4)',
                    borderRadius: '15px',
                    padding: '15px 30px',
                    color: '#22c55e',
                    fontSize: '24px',
                    fontWeight: '600',
                  }}
                >
                  {feature}
                </div>
              ))}
            </div>
          </div>

          {/* URL */}
          <div
            style={{
              position: 'absolute',
              bottom: '40px',
              fontSize: '28px',
              color: '#71717a',
              fontWeight: '500',
            }}
          >
            chessticks.vercel.app
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 1200,
      }
    )
  } catch (e: any) {
    console.log(`${e.message}`)
    return new Response(`Failed to generate the image`, {
      status: 500,
    })
  }
}
